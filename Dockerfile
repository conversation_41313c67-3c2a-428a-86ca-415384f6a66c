# Étape 1 : Build de l'application
FROM maven:latest AS build
WORKDIR /app
COPY pom.xml .
COPY . .
RUN mvn clean package -DskipTests

# Étape 2 : Image de production
FROM eclipse-temurin:latest
WORKDIR /app
COPY --from=build /app/web/target/*.jar app.jar

ENV SPRING_PROFILES_ACTIVE=prod

# # Configuration de la base de données PostgreSQL
# ENV DB_HOST=localhost
# ENV DB_PORT=5432
# ENV DB_NAME=postgres
# ENV DB_USERNAME=root
# ENV DB_PASSWORD=root
# ENV DB_DRIVER=org.postgresql.Driver

# # Configuration JWT
# ENV JWT_SECRET_KEY=9CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898F
# ENV JWT_DELAY=24

EXPOSE 80

ENTRYPOINT ["java", "-jar", "app.jar", "--server.port=80"]
