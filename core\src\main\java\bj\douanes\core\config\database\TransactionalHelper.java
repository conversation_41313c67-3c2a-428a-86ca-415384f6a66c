package bj.douanes.core.config.database;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
public class TransactionalHelper {

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Object runWithTransaction(ThrowableSupplier supplier) throws TransactionalHelperException {
        return supplier.supply();
    }

    @FunctionalInterface
    public interface ThrowableSupplier {
        Object supply() throws TransactionalHelperException;
    }

    public static class TransactionalHelperException extends Exception {
        public TransactionalHelperException(String message) {
            super(message);
        }

        public TransactionalHelperException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
