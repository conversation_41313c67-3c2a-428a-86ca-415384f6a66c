spring.application.name=erp_douanes_backend
logging.level.sql=debug
spring.flyway.enabled=false
spring.jpa.show-sql=true
# spring.task.scheduling.pool.size=10
#spring.mvc.pathmatch.matching-strategy=ant_path_matcher
#springdoc.swagger-ui.disable-swagger-default-url=true

#spring.jpa.hibernate.ddl-auto=update
#Comment demander a jpa de ne pas creer les tables automatiquement
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

app.security.white-list=/,/*.*,/static/**,/assets/**,/eservice/**,/v3/api-docs/**,/swagger-ui/**,/swagger,/doc,/docs,\
                        /api/auth/**,/api/asy/**,\
                        /api/admin/**,/api/transport/**,\
                        #/api/agents/**,/api/prime/**


spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

#spring.task.scheduling.pool.size=10
#spring.mvc.pathmatch.matching-strategy=ant_path_matcher

spring.profiles.active=home