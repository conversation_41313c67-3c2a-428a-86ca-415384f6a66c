server.port=8082

app.security.white-list=/**

# Active le mode debug pour tous les logs # logging.level.root=debug # logging.level.org.springframework=debug
logging.level.bj.douanes=debug

# ===================================== Datasource Configuration =====================================
app.datasources.typeNames=dgd,home,test

# #Primary DB
# #postgres database configuration
# app.datasource.primary.url=jdbc:postgresql://************:5432/dgd
# app.datasource.primary.username=root
# app.datasource.primary.password=rootoor
# app.datasource.primary.packages=bj.douanes.personal,bj.douanes.transport,bj.douanes.sydoniaplusplus,bj.douanes.core

# #Secondary DB
# #postgres database configuration
# app.datasource.secondary.url=jdbc:postgresql://************:5432/home
# app.datasource.secondary.username=root
# app.datasource.secondary.password=rootoor
# app.datasource.secondary.packages=bj.douanes.core,bj.douanes.prime,bj.douanes.Services


#Primary DB
#postgres database configuration
app.datasource.dgd.url=************************************
app.datasource.dgd.username=root
app.datasource.dgd.password=root
app.datasource.dgd.packages=bj.douanes.personal,bj.douanes.transport,bj.douanes.core

#Secondary DB
#postgres database configuration
app.datasource.home.url=*************************************
app.datasource.home.username=root
app.datasource.home.password=root
app.datasource.home.packages=bj.douanes.core,bj.douanes.prime,bj.douanes.Services

#Tertiary DB
#postgres database configuration
app.datasource.test.url=*************************************
app.datasource.test.username=root
app.datasource.test.password=root
app.datasource.test.packages=bj.douanes.sydoniaplusplus

# ===================================== END Datasource Configuration =====================================

#--------------------------
spring.mail.host=mail.finances.bj
spring.mail.properties.mail.smtp.localhost=127.0.0.1
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=BJDgD2022_
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.debug=true