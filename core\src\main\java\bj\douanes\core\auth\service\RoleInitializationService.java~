package bj.douanes.core.auth.service;

import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Service d'initialisation des rôles par défaut
 * S'exécute au démarrage de l'application pour s'assurer que les rôles de base existent
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoleInitializationService implements ApplicationRunner {
    
    private final CrudService roleService;
    
    @Override
    public void run(ApplicationArguments args) throws Exception {        
        try {
            roleService.initializeDefaultRoles();
        } catch (Exception e) {
            log.error("Erreur lors de l'initialisation des roles par defaut: {}", e.getMessage(), e);
            // Ne pas faire échouer le démarrage de l'application
        }
    }
}
