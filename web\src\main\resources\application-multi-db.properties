# Configuration multi-datasources exemple
server.port=8082

# Définition des datasources disponibles
app.datasources.typeNames=primary,transport_db,oracle_db

# Primary DB (PostgreSQL) - pour core, personal, prime
app.datasource.primary.url=*****************************************
app.datasource.primary.username=postgres
app.datasource.primary.password=password
app.datasource.primary.driver-class-name=org.postgresql.Driver
app.datasource.primary.dialect=org.hibernate.dialect.PostgreSQLDialect
app.datasource.primary.packages=bj.douanes.personal,bj.douanes.core,bj.douanes.prime

# Transport DB (PostgreSQL séparée)
app.datasource.transport_db.url=**********************************************
app.datasource.transport_db.username=postgres
app.datasource.transport_db.password=password
app.datasource.transport_db.driver-class-name=org.postgresql.Driver
app.datasource.transport_db.dialect=org.hibernate.dialect.PostgreSQLDialect
app.datasource.transport_db.packages=bj.douanes.transport

# Oracle DB (pour sydoniaplusplus)
app.datasource.oracle_db.url=*******************************************
app.datasource.oracle_db.username=postgres
app.datasource.oracle_db.password=password
app.datasource.oracle_db.driver-class-name=org.postgresql.Driver
app.datasource.oracle_db.dialect=org.hibernate.dialect.PostgreSQLDialect
app.datasource.oracle_db.packages=bj.douanes.sydoniaplusplus
